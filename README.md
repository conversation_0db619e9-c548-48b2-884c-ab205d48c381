# تطبيق القرآن الكريم - Quran App

تطبيق أندرويد جميل للقرآن الكريم مطور باستخدام Unity، يتضمن واجهة مستخدم عربية جميلة وإمكانية الاستماع لتلاوات مختلف القراء المشهورين.

## المميزات الرئيسية

### 📖 قراءة القرآن الكريم
- عرض جميع سور القرآن الكريم بالخط العربي الجميل
- إمكانية التنقل السهل بين السور والآيات
- عرض أرقام الآيات بوضوح
- دعم التمرير السلس للنصوص الطويلة

### 🎵 التلاوات الصوتية
- مجموعة من أشهر القراء:
  - عبد الرحمن السديس
  - مشاري راشد العفاسي
  - سعد الغامدي
  - ماهر المعيقلي
  - أحمد العجمي
- إمكانية تشغيل وإيقاف التلاوة
- عرض تقدم التشغيل
- التنقل بين الآيات أثناء التشغيل

### ⚙️ الإعدادات والتخصيص
- تغيير حجم الخط (16-36 بكسل)
- الوضع الليلي والنهاري
- إمكانية إخفاء الترجمة الإنجليزية
- حفظ الإعدادات تلقائياً

### 🎨 واجهة المستخدم
- تصميم عربي جميل ومتجاوب
- دعم الاتجاه من اليمين إلى اليسار (RTL)
- ألوان مريحة للعين
- تنقل سهل وبديهي

## بنية المشروع

```
Assets/
├── Scripts/
│   ├── QuranManager.cs      # إدارة بيانات القرآن والتنقل
│   ├── UIManager.cs         # إدارة واجهة المستخدم والإعدادات
│   └── AudioManager.cs      # إدارة التشغيل الصوتي
├── StreamingAssets/
│   └── quran_data.json      # بيانات القرآن الكريم (10 سور + 5 قراء)
└── Resources/
    └── Audio/               # ملفات التلاوات الصوتية (اختيارية)
```

## الملفات الرئيسية

### QuranManager.cs
يدير جميع عمليات القرآن الكريم:
- تحميل بيانات السور والآيات من JSON
- إدارة القراء المختلفين
- التنقل بين السور والآيات
- إنشاء قائمة السور

### UIManager.cs
يدير واجهة المستخدم:
- القائمة الرئيسية
- إعدادات التطبيق
- الوضع الليلي/النهاري
- حجم الخط

### AudioManager.cs
يدير التشغيل الصوتي:
- تشغيل التلاوات من الإنترنت أو محلياً
- التحكم في التشغيل (تشغيل/إيقاف/توقف)
- عرض تقدم التشغيل
- التنقل الصوتي

## إعداد المشروع

### متطلبات النظام
- Unity 2020.3 LTS أو أحدث
- Android SDK
- JDK 8 أو أحدث

### خطوات الإعداد

1. **إنشاء مشروع Unity جديد**
   ```
   - افتح Unity Hub
   - اختر "New Project"
   - اختر "2D" template
   - اسم المشروع: "QuranApp"
   ```

2. **إعداد إعدادات Android**
   ```
   File > Build Settings > Android
   - اختر Android platform
   - اضغط "Switch Platform"
   ```

3. **إعداد Player Settings**
   ```
   Edit > Project Settings > Player
   - Company Name: "QuranApp"
   - Product Name: "القرآن الكريم"
   - Package Name: "com.quranapp.quran"
   - Minimum API Level: Android 5.0 (API 21)
   ```

4. **إضافة الملفات**
   - انسخ جميع ملفات Scripts إلى مجلد Assets/Scripts
   - انسخ quran_data.json إلى Assets/StreamingAssets

## إنشاء واجهة المستخدم

### 1. إنشاء Canvas رئيسي
```
GameObject > UI > Canvas
- اسم: "MainCanvas"
- Render Mode: Screen Space - Overlay
- Canvas Scaler: Scale With Screen Size
- Reference Resolution: 1080x1920
```

### 2. إنشاء القائمة الرئيسية
```
- Panel: "MainMenuPanel"
  - Button: "ReadQuranButton" (قراءة القرآن)
  - Button: "SettingsButton" (الإعدادات)
  - Button: "AboutButton" (حول التطبيق)
```

### 3. إنشاء قائمة السور
```
- Panel: "SurahListPanel"
  - ScrollView: "SurahScrollView"
    - Content: "SurahListContent"
      - Button Prefab: "SurahButtonPrefab"
```

### 4. إنشاء شاشة القراءة
```
- Panel: "ReadingPanel"
  - Text: "CurrentSurahTitle"
  - ScrollView: "AyahScrollView"
    - Text: "AyahText"
  - Audio Controls Panel
```

## إضافة التلاوات الصوتية

### الطريقة الأولى: ملفات محلية
1. إنشاء مجلد `Assets/Resources/Audio`
2. إضافة ملفات MP3 بالتسمية: `surah_001.mp3`, `surah_002.mp3`, إلخ
3. تعيين Audio Type إلى "AudioClip"

### الطريقة الثانية: التحميل من الإنترنت
- استخدام UnityWebRequest لتحميل الملفات
- مواقع مقترحة للتلاوات:
  - https://server8.mp3quran.net/
  - https://download.quranicaudio.com/

## البناء للأندرويد

### 1. إعداد Build Settings
```
File > Build Settings
- Platform: Android
- Texture Compression: ETC2
- Build System: Gradle
```

### 2. إعداد Player Settings
```
- Configuration: Release
- Scripting Backend: IL2CPP
- Target Architectures: ARM64
- Internet Access: Require
```

### 3. إنشاء APK
```
- اضغط "Build"
- اختر مجلد الحفظ
- انتظر انتهاء البناء
```

## الاختبار والتطوير

### اختبار في Unity Editor
1. تشغيل المشهد الرئيسي
2. اختبار التنقل بين القوائم
3. اختبار تحميل البيانات
4. اختبار الإعدادات

### اختبار على الجهاز
1. تمكين Developer Options على الأندرويد
2. تمكين USB Debugging
3. Build and Run من Unity

## التطوير المستقبلي

### مميزات مقترحة
- [ ] البحث في القرآن الكريم
- [ ] العلامات المرجعية
- [ ] التفسير
- [ ] الترجمات بلغات مختلفة
- [ ] مشاركة الآيات
- [ ] تذكيرات القراءة
- [ ] إحصائيات القراءة

### تحسينات تقنية
- [ ] تحسين الأداء
- [ ] دعم الأجهزة اللوحية
- [ ] دعم الوضع الأفقي
- [ ] تحسين استهلاك البطارية

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في GitHub أو التواصل عبر البريد الإلكتروني.

---

**جزاكم الله خيراً** 🤲
