# دليل التطوير - تطبيق القرآن الكريم

## خطوات إعداد المشروع في Unity

### 1. إنشاء مشروع Unity جديد

```bash
# افتح Unity Hub
# اختر "New Project"
# اختر "2D" template
# اسم المشروع: "QuranApp"
# مسار المشروع: اختر المجلد المناسب
```

### 2. إعداد إعدادات المشروع

#### أ. إعدادات Player
```
Edit > Project Settings > Player
- Company Name: "QuranApp"
- Product Name: "القرآن الكريم - Quran App"
- Default Icon: (أضف أيقونة التطبيق)
- Default Cursor: (اتركه فارغ)
```

#### ب. إعدادات Android
```
File > Build Settings > Android
- اضغط "Switch Platform"

Player Settings > Android:
- Package Name: "com.quranapp.quran"
- Version: "1.0"
- Bundle Version Code: 1
- Minimum API Level: Android 5.0 (API 21)
- Target API Level: Automatic (highest installed)
- Scripting Backend: IL2CPP
- Target Architectures: ARM64 ✓
```

### 3. إنشاء بنية المجلدات

```
Assets/
├── Scenes/
│   └── MainScene.unity
├── Scripts/
│   ├── QuranManager.cs
│   ├── UIManager.cs
│   └── AudioManager.cs
├── Prefabs/
│   └── SurahButtonPrefab.prefab
├── StreamingAssets/
│   └── quran_data.json
├── Resources/
│   ├── Audio/
│   ├── Fonts/
│   └── Images/
└── Materials/
```

### 4. إنشاء واجهة المستخدم

#### أ. إنشاء Canvas الرئيسي
```
GameObject > UI > Canvas
- اسم: "MainCanvas"
- Render Mode: Screen Space - Overlay
- Canvas Scaler: Scale With Screen Size
- Reference Resolution: 1080x1920 (Portrait)
- Screen Match Mode: Match Width Or Height
- Match: 0.5
```

#### ب. إنشاء EventSystem
```
GameObject > UI > Event System
(يتم إنشاؤه تلقائياً مع Canvas)
```

#### ج. إنشاء القائمة الرئيسية
```
1. إنشاء Panel:
   - اسم: "MainMenuPanel"
   - Anchor: Stretch
   - Color: أبيض مع شفافية خفيفة

2. إضافة عنوان:
   - GameObject > UI > Text
   - اسم: "TitleText"
   - النص: "القرآن الكريم"
   - Font Size: 36
   - Alignment: Center
   - Color: أخضر داكن

3. إضافة أزرار:
   - Button 1: "قراءة القرآن"
   - Button 2: "الإعدادات"
   - Button 3: "حول التطبيق"
```

#### د. إنشاء قائمة السور
```
1. إنشاء Panel:
   - اسم: "SurahListPanel"
   - مخفي في البداية (Active: false)

2. إضافة ScrollView:
   - GameObject > UI > Scroll View
   - اسم: "SurahScrollView"
   - Content Size Fitter: Vertical Fit: Preferred Size

3. إضافة Vertical Layout Group للـ Content:
   - Spacing: 10
   - Child Alignment: Upper Center
   - Child Controls Size: Width ✓, Height ✓
```

#### هـ. إنشاء شاشة القراءة
```
1. إنشاء Panel:
   - اسم: "ReadingPanel"
   - مخفي في البداية

2. إضافة عنوان السورة:
   - Text: "CurrentSurahTitle"
   - Font Size: 28
   - Alignment: Center

3. إضافة ScrollView للآيات:
   - اسم: "AyahScrollView"
   - Text: "AyahText"
   - Font Size: 24
   - Alignment: Right (للنص العربي)
   - Rich Text: ✓

4. إضافة أزرار التحكم:
   - "السابق" | "التالي" | "القائمة الرئيسية"
```

### 5. إعداد الصوتيات

#### أ. إضافة AudioSource
```
GameObject > Audio > Audio Source
- اسم: "QuranAudioSource"
- Play On Awake: false
- Loop: false
- Volume: 0.8
```

#### ب. إنشاء أزرار التحكم الصوتي
```
- Play Button: "تشغيل"
- Pause Button: "إيقاف مؤقت"
- Stop Button: "توقف"
- Progress Slider: شريط التقدم
- Volume Slider: مستوى الصوت
```

### 6. إضافة الخطوط العربية

#### أ. تحميل خط عربي
```
1. حمل خط عربي مثل "Amiri" أو "Scheherazade"
2. ضعه في Assets/Resources/Fonts/
3. اختر الخط في Text Components
```

#### ب. إعداد النص العربي
```
- Text Direction: Right to Left
- Alignment: Right
- Rich Text: ✓ (لدعم التنسيق)
```

### 7. إضافة البيانات

#### أ. إنشاء مجلد StreamingAssets
```
Assets > Create > Folder
اسم: "StreamingAssets"
```

#### ب. إضافة ملف JSON
```
انسخ ملف quran_data.json إلى StreamingAssets/
```

### 8. ربط Scripts بالـ GameObjects

#### أ. QuranManager
```
1. إنشاء Empty GameObject
2. اسم: "QuranManager"
3. إضافة Script: QuranManager.cs
4. ربط المراجع في Inspector:
   - Surah List Panel
   - Reading Panel
   - Surah List Content
   - Surah Button Prefab
   - Current Surah Title
   - Ayah Text
   - Audio Source
   - Reciter Dropdown
```

#### ب. UIManager
```
1. إضافة Script للـ MainCanvas
2. ربط المراجع:
   - Main Menu Panel
   - Settings Panel
   - About Panel
   - جميع الأزرار
```

#### ج. AudioManager
```
1. إضافة Script للـ AudioSource GameObject
2. ربط المراجع:
   - أزرار التحكم الصوتي
   - Progress Slider
   - Now Playing Text
```

### 9. إنشاء Prefabs

#### أ. Surah Button Prefab
```
1. إنشاء Button في الـ Scene
2. تخصيص التصميم:
   - الحجم: 350x80
   - الخط: عربي
   - الألوان: مناسبة للتطبيق
3. سحبه إلى مجلد Prefabs
4. حذف النسخة من الـ Scene
```

### 10. اختبار التطبيق

#### أ. اختبار في Unity Editor
```
1. تشغيل الـ Scene
2. اختبار التنقل بين القوائم
3. اختبار تحميل البيانات
4. اختبار الصوتيات (إذا توفرت)
```

#### ب. بناء للأندرويد
```
File > Build Settings
1. إضافة الـ Scenes
2. اختيار Android Platform
3. Player Settings:
   - Orientation: Portrait
   - Graphics APIs: OpenGLES3, OpenGLES2
4. Build
```

### 11. تحسينات الأداء

#### أ. تحسين الصور
```
- استخدم تنسيق مضغوط للصور
- قلل دقة الصور غير المهمة
- استخدم Sprite Atlas للصور الصغيرة
```

#### ب. تحسين النصوص
```
- استخدم TextMeshPro بدلاً من Text العادي
- فعل Rich Text فقط عند الحاجة
- استخدم Object Pooling للنصوص المتكررة
```

#### ج. تحسين الصوتيات
```
- اضغط ملفات الصوت
- استخدم تنسيق OGG للأندرويد
- حمل الصوتيات عند الحاجة فقط
```

### 12. إضافة ميزات متقدمة

#### أ. البحث
```
1. إضافة Input Field للبحث
2. إنشاء SearchManager.cs
3. فهرسة النصوص للبحث السريع
```

#### ب. العلامات المرجعية
```
1. إنشاء BookmarkManager.cs
2. حفظ البيانات في PlayerPrefs
3. إضافة واجهة لإدارة العلامات
```

#### ج. الإعدادات المتقدمة
```
1. اختيار اللغة
2. حجم الخط المتغير
3. ألوان مخصصة
4. إعدادات الصوت
```

### 13. نشر التطبيق

#### أ. إعداد الأيقونات
```
- أيقونة التطبيق: 512x512
- أيقونات مختلفة الأحجام للأندرويد
- Splash Screen مخصص
```

#### ب. إعداد Google Play
```
1. إنشاء حساب مطور
2. رفع APK
3. إضافة الوصف والصور
4. تحديد الفئة العمرية
```

## نصائح مهمة

### الأداء
- استخدم Object Pooling للعناصر المتكررة
- تجنب تحديث UI في كل إطار
- استخدم Coroutines للعمليات الطويلة

### التصميم
- اتبع مبادئ Material Design
- استخدم ألوان متناسقة
- اجعل الواجهة بسيطة وواضحة

### الاختبار
- اختبر على أجهزة مختلفة
- اختبر مع اتصال إنترنت ضعيف
- اختبر استهلاك البطارية

### الأمان
- لا تضع مفاتيح API في الكود
- استخدم HTTPS للتحميل
- تحقق من صحة البيانات

---

**بالتوفيق في تطوير التطبيق! 🚀**
