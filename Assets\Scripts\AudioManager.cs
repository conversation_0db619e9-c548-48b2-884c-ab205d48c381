using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;

public class AudioManager : MonoBehaviour
{
    [Header("Audio Controls")]
    public Button playButton;
    public Button pauseButton;
    public Button stopButton;
    public Button nextButton;
    public Button previousButton;
    
    [Header("Audio Info")]
    public Slider progressSlider;
    public Text currentTimeText;
    public Text totalTimeText;
    public Text nowPlayingText;
    
    [Header("Audio Source")]
    public AudioSource audioSource;
    
    private QuranManager quranManager;
    private bool isPlaying = false;
    private bool isPaused = false;
    private float audioLength = 0f;
    
    void Start()
    {
        quranManager = FindObjectOfType<QuranManager>();
        SetupAudioControls();
    }
    
    void Update()
    {
        if (isPlaying && audioSource.isPlaying)
        {
            UpdateProgressSlider();
            UpdateTimeDisplay();
        }
    }
    
    void SetupAudioControls()
    {
        if (playButton != null)
            playButton.onClick.AddListener(PlayAudio);
            
        if (pauseButton != null)
            pauseButton.onClick.AddListener(PauseAudio);
            
        if (stopButton != null)
            stopButton.onClick.AddListener(StopAudio);
            
        if (nextButton != null)
            nextButton.onClick.AddListener(NextAyah);
            
        if (previousButton != null)
            previousButton.onClick.AddListener(PreviousAyah);
            
        if (progressSlider != null)
            progressSlider.onValueChanged.AddListener(OnProgressSliderChanged);
    }
    
    public void PlayAudio()
    {
        if (isPaused)
        {
            audioSource.UnPause();
            isPaused = false;
        }
        else
        {
            // Load and play current surah audio
            StartCoroutine(LoadAndPlayAudio());
        }
        
        isPlaying = true;
        UpdatePlayButtonState();
    }
    
    public void PauseAudio()
    {
        if (audioSource.isPlaying)
        {
            audioSource.Pause();
            isPaused = true;
            isPlaying = false;
            UpdatePlayButtonState();
        }
    }
    
    public void StopAudio()
    {
        audioSource.Stop();
        isPlaying = false;
        isPaused = false;
        
        if (progressSlider != null)
            progressSlider.value = 0f;
            
        UpdateTimeDisplay();
        UpdatePlayButtonState();
    }
    
    public void NextAyah()
    {
        if (quranManager != null)
        {
            quranManager.NextAyah();
            if (isPlaying)
            {
                StopAudio();
                PlayAudio();
            }
        }
    }
    
    public void PreviousAyah()
    {
        if (quranManager != null)
        {
            quranManager.PreviousAyah();
            if (isPlaying)
            {
                StopAudio();
                PlayAudio();
            }
        }
    }
    
    IEnumerator LoadAndPlayAudio()
    {
        if (quranManager == null) yield break;
        
        // Construct audio URL based on current reciter and surah
        string audioUrl = GetAudioUrl();
        
        if (string.IsNullOrEmpty(audioUrl))
        {
            Debug.LogWarning("Audio URL is empty");
            yield break;
        }
        
        using (UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip(audioUrl, AudioType.MPEG))
        {
            yield return www.SendWebRequest();
            
            if (www.result == UnityWebRequest.Result.Success)
            {
                AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
                audioSource.clip = clip;
                audioLength = clip.length;
                
                audioSource.Play();
                UpdateNowPlayingText();
                
                if (progressSlider != null)
                {
                    progressSlider.maxValue = audioLength;
                    progressSlider.value = 0f;
                }
            }
            else
            {
                Debug.LogError("Failed to load audio: " + www.error);
                // Play local audio file if available
                PlayLocalAudio();
            }
        }
    }
    
    void PlayLocalAudio()
    {
        // Try to load local audio file
        string fileName = $"surah_{quranManager.currentSurahIndex + 1:000}";
        AudioClip localClip = Resources.Load<AudioClip>($"Audio/{fileName}");
        
        if (localClip != null)
        {
            audioSource.clip = localClip;
            audioLength = localClip.length;
            audioSource.Play();
            UpdateNowPlayingText();
            
            if (progressSlider != null)
            {
                progressSlider.maxValue = audioLength;
                progressSlider.value = 0f;
            }
        }
        else
        {
            Debug.LogWarning("No local audio file found for current surah");
        }
    }
    
    string GetAudioUrl()
    {
        if (quranManager == null || quranManager.currentReciter == null)
            return "";
        
        // Example URL structure - adjust based on your audio source
        int surahNumber = quranManager.currentSurahIndex + 1;
        string reciterCode = quranManager.currentReciter.audioBaseUrl;
        
        // This is a sample URL structure - replace with actual audio service
        return $"https://server8.mp3quran.net/afs/{reciterCode}/{surahNumber:000}.mp3";
    }
    
    void UpdateProgressSlider()
    {
        if (progressSlider != null && audioSource.clip != null)
        {
            progressSlider.value = audioSource.time;
        }
    }
    
    void UpdateTimeDisplay()
    {
        if (currentTimeText != null)
        {
            float currentTime = audioSource.isPlaying ? audioSource.time : 0f;
            currentTimeText.text = FormatTime(currentTime);
        }
        
        if (totalTimeText != null)
        {
            totalTimeText.text = FormatTime(audioLength);
        }
    }
    
    void UpdateNowPlayingText()
    {
        if (nowPlayingText != null && quranManager != null)
        {
            var currentSurah = quranManager.quranData.surahs[quranManager.currentSurahIndex];
            var currentReciter = quranManager.currentReciter;
            
            nowPlayingText.text = $"الآن يُتلى: {currentSurah.arabicName}\nبصوت: {currentReciter.arabicName}";
        }
    }
    
    void UpdatePlayButtonState()
    {
        if (playButton != null)
            playButton.interactable = !isPlaying;
            
        if (pauseButton != null)
            pauseButton.interactable = isPlaying;
    }
    
    void OnProgressSliderChanged(float value)
    {
        if (audioSource.clip != null && !audioSource.isPlaying)
        {
            audioSource.time = value;
        }
    }
    
    string FormatTime(float timeInSeconds)
    {
        int minutes = Mathf.FloorToInt(timeInSeconds / 60f);
        int seconds = Mathf.FloorToInt(timeInSeconds % 60f);
        return string.Format("{0:00}:{1:00}", minutes, seconds);
    }
    
    void OnDestroy()
    {
        StopAllCoroutines();
    }
}
