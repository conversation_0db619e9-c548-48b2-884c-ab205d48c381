using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.IO;

[System.Serializable]
public class Surah
{
    public int number;
    public string arabicName;
    public string englishName;
    public string revelationType;
    public int numberOfAyahs;
    public List<string> ayahs;
}

[System.Serializable]
public class QuranData
{
    public List<Surah> surahs;
}

[System.Serializable]
public class Reciter
{
    public string name;
    public string arabicName;
    public string audioBaseUrl;
    public bool isActive;
}

public class QuranManager : MonoBehaviour
{
    [Header("UI References")]
    public GameObject surahListPanel;
    public GameObject readingPanel;
    public Transform surahListContent;
    public GameObject surahButtonPrefab;
    public Text currentSurahTitle;
    public Text ayahText;
    public ScrollRect ayahScrollRect;
    
    [Header("Audio")]
    public AudioSource audioSource;
    public Dropdown reciterDropdown;
    
    [Header("Data")]
    public QuranData quranData;
    public List<Reciter> reciters;
    
    private int currentSurahIndex = 0;
    private int currentAyahIndex = 0;
    private Reciter currentReciter;
    
    void Start()
    {
        LoadQuranData();
        SetupReciters();
        CreateSurahList();
        ShowSurahList();
    }
    
    void LoadQuranData()
    {
        // Load Quran data from JSON file
        string filePath = Path.Combine(Application.streamingAssetsPath, "quran_data.json");
        
        if (File.Exists(filePath))
        {
            string dataAsJson = File.ReadAllText(filePath);
            quranData = JsonUtility.FromJson<QuranData>(dataAsJson);
        }
        else
        {
            Debug.LogError("Quran data file not found!");
            CreateSampleData();
        }
    }
    
    void CreateSampleData()
    {
        // Create sample data for testing
        quranData = new QuranData();
        quranData.surahs = new List<Surah>();
        
        // Add Al-Fatiha as sample
        Surah fatiha = new Surah();
        fatiha.number = 1;
        fatiha.arabicName = "الفاتحة";
        fatiha.englishName = "Al-Fatiha";
        fatiha.revelationType = "Meccan";
        fatiha.numberOfAyahs = 7;
        fatiha.ayahs = new List<string>
        {
            "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
            "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
            "الرَّحْمَٰنِ الرَّحِيمِ",
            "مَالِكِ يَوْمِ الدِّينِ",
            "إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ",
            "اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ",
            "صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ"
        };
        
        quranData.surahs.Add(fatiha);
    }
    
    void SetupReciters()
    {
        reciters = new List<Reciter>
        {
            new Reciter { name = "Abdul Rahman Al-Sudais", arabicName = "عبد الرحمن السديس", audioBaseUrl = "sudais", isActive = true },
            new Reciter { name = "Mishary Rashid Alafasy", arabicName = "مشاري راشد العفاسي", audioBaseUrl = "alafasy", isActive = true },
            new Reciter { name = "Saad Al-Ghamdi", arabicName = "سعد الغامدي", audioBaseUrl = "ghamdi", isActive = true },
            new Reciter { name = "Maher Al-Muaiqly", arabicName = "ماهر المعيقلي", audioBaseUrl = "muaiqly", isActive = true },
            new Reciter { name = "Ahmed Al-Ajmi", arabicName = "أحمد العجمي", audioBaseUrl = "ajmi", isActive = true }
        };
        
        currentReciter = reciters[0];
        PopulateReciterDropdown();
    }
    
    void PopulateReciterDropdown()
    {
        if (reciterDropdown != null)
        {
            reciterDropdown.ClearOptions();
            List<string> options = new List<string>();
            
            foreach (Reciter reciter in reciters)
            {
                if (reciter.isActive)
                {
                    options.Add(reciter.arabicName + " - " + reciter.name);
                }
            }
            
            reciterDropdown.AddOptions(options);
            reciterDropdown.onValueChanged.AddListener(OnReciterChanged);
        }
    }
    
    void CreateSurahList()
    {
        if (surahListContent == null || surahButtonPrefab == null) return;
        
        // Clear existing buttons
        foreach (Transform child in surahListContent)
        {
            Destroy(child.gameObject);
        }
        
        // Create buttons for each surah
        for (int i = 0; i < quranData.surahs.Count; i++)
        {
            GameObject buttonObj = Instantiate(surahButtonPrefab, surahListContent);
            Button button = buttonObj.GetComponent<Button>();
            Text buttonText = buttonObj.GetComponentInChildren<Text>();
            
            Surah surah = quranData.surahs[i];
            buttonText.text = $"{surah.number}. {surah.arabicName}\n{surah.englishName}";
            
            int surahIndex = i;
            button.onClick.AddListener(() => OpenSurah(surahIndex));
        }
    }
    
    public void ShowSurahList()
    {
        surahListPanel.SetActive(true);
        readingPanel.SetActive(false);
    }
    
    public void OpenSurah(int surahIndex)
    {
        currentSurahIndex = surahIndex;
        currentAyahIndex = 0;
        
        Surah surah = quranData.surahs[surahIndex];
        currentSurahTitle.text = $"{surah.number}. {surah.arabicName} - {surah.englishName}";
        
        DisplayCurrentAyah();
        
        surahListPanel.SetActive(false);
        readingPanel.SetActive(true);
    }
    
    void DisplayCurrentAyah()
    {
        if (currentSurahIndex < quranData.surahs.Count)
        {
            Surah currentSurah = quranData.surahs[currentSurahIndex];
            
            // Display all ayahs of the surah
            string fullText = "";
            for (int i = 0; i < currentSurah.ayahs.Count; i++)
            {
                fullText += $"({i + 1}) {currentSurah.ayahs[i]}\n\n";
            }
            
            ayahText.text = fullText;
        }
    }
    
    public void NextAyah()
    {
        Surah currentSurah = quranData.surahs[currentSurahIndex];
        if (currentAyahIndex < currentSurah.ayahs.Count - 1)
        {
            currentAyahIndex++;
            DisplayCurrentAyah();
        }
    }
    
    public void PreviousAyah()
    {
        if (currentAyahIndex > 0)
        {
            currentAyahIndex--;
            DisplayCurrentAyah();
        }
    }
    
    public void PlayAudio()
    {
        // This would play audio for the current surah
        // Implementation depends on audio file structure
        Debug.Log($"Playing audio for Surah {currentSurahIndex + 1} by {currentReciter.name}");
    }
    
    public void OnReciterChanged(int index)
    {
        if (index < reciters.Count)
        {
            currentReciter = reciters[index];
            Debug.Log($"Reciter changed to: {currentReciter.name}");
        }
    }
}
