using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIManager : MonoBehaviour
{
    [Header("Main Menu")]
    public GameObject mainMenuPanel;
    public Button readQuranButton;
    public Button settingsButton;
    public Button aboutButton;
    
    [Header("Settings Panel")]
    public GameObject settingsPanel;
    public Slider fontSizeSlider;
    public Toggle arabicOnlyToggle;
    public Toggle nightModeToggle;
    public Button backFromSettingsButton;
    
    [Header("About Panel")]
    public GameObject aboutPanel;
    public Button backFromAboutButton;
    
    [Header("Theme Settings")]
    public Image backgroundImage;
    public Color dayModeColor = Color.white;
    public Color nightModeColor = new Color(0.1f, 0.1f, 0.1f, 1f);
    
    private QuranManager quranManager;
    private bool isNightMode = false;
    private float currentFontSize = 24f;
    
    void Start()
    {
        quranManager = FindObjectOfType<QuranManager>();
        SetupUI();
        LoadSettings();
    }
    
    void SetupUI()
    {
        // Setup button listeners
        if (readQuranButton != null)
            readQuranButton.onClick.AddListener(OpenQuranReader);
            
        if (settingsButton != null)
            settingsButton.onClick.AddListener(OpenSettings);
            
        if (aboutButton != null)
            aboutButton.onClick.AddListener(OpenAbout);
            
        if (backFromSettingsButton != null)
            backFromSettingsButton.onClick.AddListener(CloseSettings);
            
        if (backFromAboutButton != null)
            backFromAboutButton.onClick.AddListener(CloseAbout);
        
        // Setup settings controls
        if (fontSizeSlider != null)
        {
            fontSizeSlider.minValue = 16f;
            fontSizeSlider.maxValue = 36f;
            fontSizeSlider.value = currentFontSize;
            fontSizeSlider.onValueChanged.AddListener(OnFontSizeChanged);
        }
        
        if (nightModeToggle != null)
            nightModeToggle.onValueChanged.AddListener(OnNightModeToggled);
            
        if (arabicOnlyToggle != null)
            arabicOnlyToggle.onValueChanged.AddListener(OnArabicOnlyToggled);
    }
    
    void LoadSettings()
    {
        // Load saved settings
        currentFontSize = PlayerPrefs.GetFloat("FontSize", 24f);
        isNightMode = PlayerPrefs.GetInt("NightMode", 0) == 1;
        bool arabicOnly = PlayerPrefs.GetInt("ArabicOnly", 0) == 1;
        
        // Apply settings
        if (fontSizeSlider != null)
            fontSizeSlider.value = currentFontSize;
            
        if (nightModeToggle != null)
            nightModeToggle.isOn = isNightMode;
            
        if (arabicOnlyToggle != null)
            arabicOnlyToggle.isOn = arabicOnly;
            
        ApplyTheme();
        ApplyFontSize();
    }
    
    void SaveSettings()
    {
        PlayerPrefs.SetFloat("FontSize", currentFontSize);
        PlayerPrefs.SetInt("NightMode", isNightMode ? 1 : 0);
        PlayerPrefs.SetInt("ArabicOnly", arabicOnlyToggle != null ? (arabicOnlyToggle.isOn ? 1 : 0) : 0);
        PlayerPrefs.Save();
    }
    
    public void OpenQuranReader()
    {
        mainMenuPanel.SetActive(false);
        if (quranManager != null)
            quranManager.ShowSurahList();
    }
    
    public void OpenSettings()
    {
        mainMenuPanel.SetActive(false);
        settingsPanel.SetActive(true);
    }
    
    public void OpenAbout()
    {
        mainMenuPanel.SetActive(false);
        aboutPanel.SetActive(true);
    }
    
    public void CloseSettings()
    {
        SaveSettings();
        settingsPanel.SetActive(false);
        mainMenuPanel.SetActive(true);
    }
    
    public void CloseAbout()
    {
        aboutPanel.SetActive(false);
        mainMenuPanel.SetActive(true);
    }
    
    public void BackToMainMenu()
    {
        // Hide all panels and show main menu
        if (quranManager != null)
        {
            quranManager.surahListPanel.SetActive(false);
            quranManager.readingPanel.SetActive(false);
        }
        
        settingsPanel.SetActive(false);
        aboutPanel.SetActive(false);
        mainMenuPanel.SetActive(true);
    }
    
    void OnFontSizeChanged(float newSize)
    {
        currentFontSize = newSize;
        ApplyFontSize();
    }
    
    void OnNightModeToggled(bool isOn)
    {
        isNightMode = isOn;
        ApplyTheme();
    }
    
    void OnArabicOnlyToggled(bool isOn)
    {
        // This would hide/show English translations
        Debug.Log("Arabic only mode: " + isOn);
    }
    
    void ApplyTheme()
    {
        if (backgroundImage != null)
        {
            backgroundImage.color = isNightMode ? nightModeColor : dayModeColor;
        }
        
        // Apply theme to text components
        Text[] allTexts = FindObjectsOfType<Text>();
        foreach (Text text in allTexts)
        {
            if (text.gameObject.name.Contains("Title") || text.gameObject.name.Contains("Ayah"))
            {
                text.color = isNightMode ? Color.white : Color.black;
            }
        }
    }
    
    void ApplyFontSize()
    {
        // Apply font size to Quran text
        if (quranManager != null && quranManager.ayahText != null)
        {
            quranManager.ayahText.fontSize = (int)currentFontSize;
        }
    }
    
    public void QuitApplication()
    {
        Application.Quit();
    }
}
