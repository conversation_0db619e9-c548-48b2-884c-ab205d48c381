# تعليمات البناء - تطبيق القرآن الكريم

## متطلبات النظام

### البرامج المطلوبة
- **Unity 2020.3 LTS** أو أحدث
- **Android Studio** (للـ Android SDK)
- **JDK 8** أو أحدث
- **Git** (اختياري)

### إعدادات النظام
- **نظام التشغيل**: Windows 10/11, macOS, أو Linux
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مفضل)
- **مساحة القرص**: 10 GB مساحة فارغة
- **معالج الرسوميات**: دعم DirectX 11

## إعداد البيئة

### 1. تثبيت Unity
```bash
# حمل Unity Hub من الموقع الرسمي
# ثبت Unity 2020.3 LTS مع Android Build Support
# تأكد من تثبيت:
# - Android SDK & NDK Tools
# - OpenJDK
```

### 2. <PERSON>عد<PERSON> Android SDK
```bash
# افتح Unity > Preferences > External Tools
# تأكد من مسارات:
# - Android SDK
# - Android NDK
# - JDK
```

### 3. إعداد الجهاز للاختبار
```bash
# على جهاز الأندرويد:
# Settings > About Phone > Build Number (اضغط 7 مرات)
# Settings > Developer Options > USB Debugging (فعل)
```

## خطوات البناء

### 1. فتح المشروع
```bash
# افتح Unity Hub
# اضغط "Open"
# اختر مجلد المشروع
# انتظر تحميل المشروع
```

### 2. إعداد Build Settings
```bash
File > Build Settings
# Platform: Android
# اضغط "Switch Platform" (إذا لم يكن محدد)
# Scenes In Build: تأكد من إضافة MainScene
```

### 3. إعداد Player Settings
```bash
Edit > Project Settings > Player

# Company Name: "QuranApp"
# Product Name: "القرآن الكريم"
# Package Name: "com.quranapp.quran"
# Version: "1.0.0"
# Bundle Version Code: 1

# Icon:
# - Default Icon: أضف أيقونة 1024x1024
# - Adaptive Icon: أضف أيقونات متكيفة

# Resolution and Presentation:
# - Default Orientation: Portrait
# - Allowed Orientations for Auto Rotation: Portrait ✓

# Other Settings:
# - Scripting Backend: IL2CPP
# - Target Architectures: ARM64 ✓
# - Internet Access: Require
# - Minimum API Level: Android 5.0 (API 21)
# - Target API Level: Automatic (highest installed)
```

### 4. إعداد Publishing Settings
```bash
# Keystore Manager:
# - Create New Keystore
# - Keystore Name: "quran_keystore.keystore"
# - Password: (اختر كلمة مرور قوية)
# - Alias: "quran_key"
# - Password: (نفس كلمة المرور أو مختلفة)
# - Validity: 25 years
# - Company: "QuranApp"
```

### 5. تحسين الإعدادات
```bash
# XR Settings:
# - Virtual Reality Supported: ✗

# Graphics:
# - Graphics APIs: OpenGLES3, OpenGLES2
# - Multithreaded Rendering: ✓

# Optimization:
# - Prebake Collision Meshes: ✓
# - Preloaded Assets: أضف الأصول المهمة
# - Strip Engine Code: ✓
```

## بناء التطبيق

### 1. بناء APK للاختبار
```bash
File > Build Settings
# Development Build: ✓ (للاختبار)
# Script Debugging: ✓ (للاختبار)
# اضغط "Build"
# اختر مجلد الحفظ (مثل: Builds/Debug/)
# اسم الملف: "QuranApp_Debug.apk"
```

### 2. بناء APK للنشر
```bash
File > Build Settings
# Development Build: ✗
# Script Debugging: ✗
# Compression Method: LZ4HC
# اضغط "Build"
# اختر مجلد الحفظ (مثل: Builds/Release/)
# اسم الملف: "QuranApp_v1.0.apk"
```

### 3. بناء AAB للـ Google Play
```bash
File > Build Settings
# Build App Bundle (Google Play): ✓
# اضغط "Build"
# اسم الملف: "QuranApp_v1.0.aab"
```

## اختبار التطبيق

### 1. اختبار على المحاكي
```bash
# افتح Android Studio
# AVD Manager > Create Virtual Device
# اختر جهاز (مثل: Pixel 4)
# API Level: 28 أو أحدث
# ثبت APK على المحاكي
```

### 2. اختبار على الجهاز الحقيقي
```bash
# وصل الجهاز بـ USB
# فعل USB Debugging
# Unity: Build and Run
# أو ثبت APK يدوياً
```

### 3. اختبارات مهمة
```bash
# وظائف أساسية:
# - تحميل قائمة السور ✓
# - فتح السور وعرض النص ✓
# - التنقل بين الآيات ✓
# - تغيير القارئ ✓
# - تشغيل الصوت (إذا توفر) ✓
# - الإعدادات (حجم الخط، الوضع الليلي) ✓

# اختبارات الأداء:
# - سرعة التحميل
# - استهلاك الذاكرة
# - استهلاك البطارية
# - الاستجابة للمس
```

## حل المشاكل الشائعة

### 1. مشاكل البناء
```bash
# خطأ: "Unable to find Android SDK"
# الحل: تحديد مسار Android SDK في Unity Preferences

# خطأ: "Gradle build failed"
# الحل: تحديث Gradle في Android Studio

# خطأ: "Keystore not found"
# الحل: إنشاء Keystore جديد أو تحديد المسار الصحيح
```

### 2. مشاكل الأداء
```bash
# التطبيق بطيء:
# - تقليل دقة الصور
# - استخدام Object Pooling
# - تحسين الكود

# استهلاك ذاكرة عالي:
# - تحسين الصور
# - تنظيف المراجع غير المستخدمة
# - استخدام Resources.UnloadUnusedAssets()
```

### 3. مشاكل الواجهة
```bash
# النص العربي لا يظهر بشكل صحيح:
# - استخدام خط يدعم العربية
# - تفعيل RTL Support
# - استخدام TextMeshPro

# الواجهة لا تتكيف مع الشاشات المختلفة:
# - استخدام Canvas Scaler
# - تحديد Anchor Points بشكل صحيح
# - اختبار على أحجام شاشة مختلفة
```

## تحسين الحجم

### 1. تقليل حجم APK
```bash
# Player Settings:
# - Strip Engine Code: ✓
# - Managed Stripping Level: High
# - Script Call Optimization: Fast but no Exceptions

# تحسين الأصول:
# - ضغط الصور
# - تقليل جودة الصوتيات
# - حذف الملفات غير المستخدمة
```

### 2. استخدام Asset Bundles
```bash
# للملفات الكبيرة (مثل التلاوات):
# - إنشاء Asset Bundles
# - تحميل عند الحاجة
# - تخزين مؤقت ذكي
```

## نشر التطبيق

### 1. إعداد Google Play Console
```bash
# إنشاء حساب مطور
# إنشاء تطبيق جديد
# رفع AAB file
# إضافة الوصف والصور
# تحديد الفئة العمرية والمحتوى
```

### 2. متطلبات النشر
```bash
# الملفات المطلوبة:
# - AAB file (موقع ومشفر)
# - أيقونة التطبيق (512x512)
# - لقطات شاشة (مختلف الأحجام)
# - وصف التطبيق (عربي وإنجليزي)
# - سياسة الخصوصية

# المعلومات المطلوبة:
# - فئة التطبيق: Books & Reference
# - التقييم المحتوى: Everyone
# - الأذونات المطلوبة: Internet
```

### 3. اختبار ما قبل النشر
```bash
# Internal Testing:
# - رفع للاختبار الداخلي
# - اختبار مع مجموعة صغيرة
# - إصلاح المشاكل

# Closed Testing:
# - اختبار مع مجموعة أكبر
# - جمع التعليقات
# - تحسين التطبيق

# Open Testing (اختياري):
# - اختبار عام محدود
# - اختبار الأداء تحت الضغط
```

## قائمة التحقق النهائية

### قبل البناء
- [ ] جميع Scripts تعمل بدون أخطاء
- [ ] جميع المراجع مربوطة في Inspector
- [ ] البيانات محملة بشكل صحيح
- [ ] الواجهة تعمل على أحجام شاشة مختلفة
- [ ] الأيقونات والصور محسنة

### قبل النشر
- [ ] اختبار شامل على أجهزة مختلفة
- [ ] التأكد من عدم وجود crashes
- [ ] اختبار الأداء والذاكرة
- [ ] مراجعة المحتوى والنصوص
- [ ] التأكد من الأذونات المطلوبة
- [ ] إعداد سياسة الخصوصية
- [ ] تحضير مواد التسويق

---

**بالتوفيق في نشر التطبيق! 🎉**
